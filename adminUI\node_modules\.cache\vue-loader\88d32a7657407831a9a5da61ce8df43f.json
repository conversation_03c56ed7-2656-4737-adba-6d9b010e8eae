{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\brand\\manage.vue?vue&type=template&id=5700eb87&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\brand\\manage.vue", "mtime": 1754364854658}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754052682065}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["\n<div class=\"divBox relative\">\n  <el-card class=\"box-card\">\n    <div slot=\"header\" class=\"clearfix\">\n      <div class=\"container mt-1\">\n        <el-form inline size=\"small\">\n          <el-form-item :label=\"$t('brand.search')\">\n            <el-input\n              v-model=\"form.name\"\n              :placeholder=\"$t('brand.brandNameInput')\"\n              class=\"selWidth\"\n              size=\"small\"\n              clearable\n            />\n          </el-form-item>\n          <el-form-item :label=\"$t('brand.status')\">\n            <el-select\n              v-model=\"form.type\"\n              :placeholder=\"$t('brand.pleaseSelect')\"\n            >\n              <el-option\n                v-for=\"item in statusOptions\"\n                :key=\"item.value\"\n                :label=\"$t(item.label)\"\n                :value=\"item.value\"\n              />\n            </el-select>\n          </el-form-item>\n        </el-form>\n      </div>\n      <el-button size=\"small\" type=\"primary\" class=\"mr10\" @click=\"onSearch\">{{\n        $t(\"brand.query\")\n      }}</el-button>\n      <el-button size=\"small\" type=\"\" class=\"mr10\" @click=\"onReset\">{{\n        $t(\"brand.reset\")\n      }}</el-button>\n\n      <div class=\"acea-row padtop-10\">\n        <el-button size=\"small\" type=\"success\" @click=\"onAdd\">{{\n          $t(\"brand.addBrand\")\n        }}</el-button>\n        <el-button\n          size=\"small\"\n          :disabled=\"multipleSelection.length === 0\"\n          @click=\"batchHandle('online')\"\n        >{{\n          $t(\"brand.batchOnline\")\n        }}</el-button>\n        <el-button\n          size=\"small\"\n          :disabled=\"multipleSelection.length === 0\"\n          @click=\"batchHandle('outline')\"\n        >{{\n          $t(\"brand.batchOffline\")\n        }}</el-button>\n        <el-button\n          size=\"small\"\n          :disabled=\"multipleSelection.length === 0\"\n          @click=\"batchHandle('delete')\"\n        >{{\n          $t(\"brand.batchDelete\")\n        }}</el-button>\n      </div>\n    </div>\n\n    <el-table\n      v-loading=\"listLoading\"\n      :data=\"tableData.data\"\n      style=\"width: 100%\"\n      size=\"mini\"\n      :highlight-current-row=\"true\"\n      :header-cell-style=\"{ fontWeight: 'bold' }\"\n      @selection-change=\"handleSelectionChange\"\n    >\n      <el-table-column type=\"selection\" width=\"55\" />\n      <el-table-column :label=\"$t('brand.brandLogo')\" min-width=\"80\">\n        <template slot-scope=\"scope\">\n          <div class=\"demo-image__preview\">\n            <el-image\n              style=\"width: 36px; height: 36px\"\n              :src=\"scope.row.logoUrl\"\n              :preview-src-list=\"[scope.row.logoUrl]\"\n            />\n          </div>\n        </template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('brand.brandName')\"\n        min-width=\"80\"\n        prop=\"name\"\n      />\n      <el-table-column\n        :label=\"$t('brand.industry')\"\n        min-width=\"160\"\n        :show-overflow-tooltip=\"true\"\n        prop=\"industry\"\n      />\n      <el-table-column\n        :label=\"$t('brand.platform')\"\n        min-width=\"90\"\n        align=\"center\"\n        prop=\"platform\"\n      />\n      <el-table-column\n        :label=\"$t('brand.productCount')\"\n        min-width=\"100\"\n        align=\"center\"\n        prop=\"productCount\"\n      />\n      <!-- <el-table-column\n        :label=\"$t('brand.maxCashback')\"\n        min-width=\"100\"\n        align=\"center\"\n        prop=\"maxCashBackRate\"\n      /> -->\n      <!-- <el-table-column\n        :label=\"$t('brand.soldCount')\"\n        min-width=\"120\"\n        align=\"center\"\n        prop=\"productSoldCount\"\n      /> -->\n      <!-- <el-table-column\n        :label=\"$t('brand.soldAmount')\"\n        min-width=\"100\"\n        align=\"center\"\n        prop=\"productSoldAmount\"\n      /> -->\n      <!-- <el-table-column\n        :label=\"$t('brand.cashbackAmount')\"\n        min-width=\"100\"\n        align=\"center\"\n        prop=\"productCashbackAmount\"\n      /> -->\n      <!-- <el-table-column\n        :label=\"$t('brand.shareCount')\"\n        min-width=\"120\"\n        align=\"center\"\n        prop=\"productShareCount\"\n      /> -->\n      <el-table-column\n        :label=\"$t('brand.createTime')\"\n        min-width=\"120\"\n        align=\"center\"\n        prop=\"gmtCreate\"\n      />\n      <el-table-column\n        :label=\"$t('brand.creator')\"\n        min-width=\"120\"\n        align=\"center\"\n        prop=\"creator\"\n      />\n      <el-table-column\n        :label=\"$t('brand.isHot')\"\n        min-width=\"120\"\n        align=\"center\"\n      >\n        <template slot-scope=\"scope\">\n          <el-switch\n            v-model=\"scope.row.isHot\"\n            active-value=\"true\"\n            @change=\"isHotChange(scope.row.id, scope.row.isHot)\"\n          />\n        </template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('brand.isHighCashback')\"\n        min-width=\"120\"\n        align=\"center\"\n      >\n        <template slot-scope=\"scope\">\n          <el-switch\n            v-model=\"scope.row.isHighCashback\"\n            active-value=\"true\"\n            @change=\"\n              isHighCashbackChange(scope.row.id, scope.row.isHighCashback)\n            \"\n          />\n        </template>\n      </el-table-column>\n\n   <!--    <el-table-column\n        :label=\"$t('brand.statusLabel')\"\n        min-width=\"120\"\n        align=\"center\"\n      >\n       <template slot-scope=\"scope\">\n          <span v-if=\"scope.row.status=='1'\">{{$t(\"product.isOnline\")}}</span><span v-else>{{$t(\"product.isOutline\")}}</span>\n        </template>\n      </el-table-column> -->\n\n      <el-table-column\n        :label=\"$t('product.action')\"\n        min-width=\"150\"\n        fixed=\"right\"\n        align=\"center\"\n      >\n        <template slot-scope=\"scope\">\n          <!-- <el-button\n            type=\"text\"\n            size=\"small\"\n            class=\"mr10\"\n            @click=\"handleUpdate(scope.row, scope.$index)\"\n          >\n            <span v-if=\"scope.row.status == '1'\">{{\n              $t(\"brand.offline\")\n            }}</span>\n            <span v-else>{{ $t(\"brand.online\") }}</span>\n          </el-button> -->\n          <router-link :to=\" { path:'/brand/product/list',query: {brand:scope.row.code} } \">\n            <el-button size=\"small\" type=\"text\" class=\"mr10\">{{ $t(\"brand.productList\")}}</el-button>\n          </router-link>\n          <el-button\n            type=\"text\"\n            size=\"small\"\n            class=\"mr10\"\n            @click=\"editBrand(scope.row, scope.$index)\"\n            >{{ $t(\"brand.edit\") }}</el-button\n          >\n          <el-button\n            type=\"text\"\n            size=\"small\"\n            @click=\"handleDelete(scope.row.id, scope.$index)\"\n            >{{ $t(\"brand.delete\") }}</el-button\n          >\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <div class=\"block\">\n      <el-pagination\n        :page-sizes=\"[20, 40, 60, 80]\"\n        :page-size=\"form.limit\"\n        :current-page=\"form.page\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"tableData.total\"\n        @size-change=\"handleSizeChange\"\n        @current-change=\"pageChange\"\n      />\n    </div>\n  </el-card>\n\n  <!-- 新增/编辑品牌对话框 -->\n  <el-dialog\n    :title=\"isEditMode ? $t('brand.editDialogTitle') : $t('brand.addDialogTitle')\"\n    :visible.sync=\"brandDialogVisible\"\n    width=\"500px\"\n    :before-close=\"handleCloseBrandDialog\"\n  >\n    <el-form\n      class=\"mt20\"\n      ref=\"dform\"\n      :model=\"dform\"\n      label-width=\"120px\"\n      @submit.native.prevent\n      v-loading=\"loading\"\n    >\n      <el-form-item :label=\"$t('brand.brandName')\">\n        <el-input\n          v-model=\"dform.name\"\n          :placeholder=\"$t('brand.brandNameInput')\"\n          class=\"selWidth\"\n          size=\"small\"\n          clearable\n        />\n      </el-form-item>\n      <el-form-item :label=\"$t('brand.brandLogo')\">\n        <el-input\n          v-model=\"dform.logoUrl\"\n          :placeholder=\"$t('brand.brandLogoInput')\"\n          class=\"selWidth\"\n          size=\"small\"\n          clearable\n        />\n      </el-form-item>\n      <el-form-item :label=\"$t('brand.industry')\">\n        <el-select\n          v-model=\"dform.industry\"\n          :placeholder=\"$t('brand.pleaseSelect')\"\n        >\n          <el-option\n            v-for=\"item in industryListOptions\"\n            :key=\"item.value\"\n            :label=\"item.label\"\n            :value=\"item.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item :label=\"$t('brand.platform')\">\n        <el-select\n          v-model=\"dform.platform\"\n          :placeholder=\"$t('brand.pleaseSelect')\"\n        >\n          <el-option\n            v-for=\"item in platformOptions\"\n            :key=\"item.value\"\n            :label=\"$t(item.label)\"\n            :value=\"item.value\"\n          />\n        </el-select>\n      </el-form-item>\n      <el-form-item :label=\"$t('brand.contactPerson')\">\n        <el-input\n          v-model=\"dform.contactPerson\"\n          :placeholder=\"$t('brand.contactPerson')\"\n          class=\"selWidth\"\n          size=\"small\"\n          clearable\n        />\n      </el-form-item>\n      <el-form-item :label=\"$t('brand.contactPhone')\">\n        <el-input\n          v-model=\"dform.contactPhone\"\n          :placeholder=\"$t('brand.contactPhone')\"\n          class=\"selWidth\"\n          size=\"small\"\n          clearable\n        />\n      </el-form-item>\n      <el-form-item :label=\"$t('brand.status')\">\n        <el-select\n          v-model=\"dform.status\"\n          :placeholder=\"$t('brand.pleaseSelect')\"\n        >\n          <el-option\n            v-for=\"item in editStatusOptions\"\n            :key=\"item.value\"\n            :label=\"$t(item.label)\"\n            :value=\"item.value\"\n          />\n        </el-select>\n      </el-form-item>\n    </el-form>\n    <span slot=\"footer\" class=\"dialog-footer\">\n      <el-button type=\"primary\" @click=\"onSubBrand\">{{\n        isEditMode ? $t(\"brand.update\") : $t(\"brand.confirm\")\n      }}</el-button>\n      <el-button @click=\"handleCloseBrandDialog\">{{\n        $t(\"brand.cancel\")\n      }}</el-button>\n    </span>\n  </el-dialog>\n</div>\n", null]}