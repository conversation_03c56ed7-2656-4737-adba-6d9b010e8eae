{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\operations\\chainTransferRecord\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\operations\\chainTransferRecord\\index.vue", "mtime": 1754364854665}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { productShareRecordApi } from \"@/api/chainTransferRecord\";\r\nimport { brandLstApi } from \"@/api/brand\";\r\nexport default {\r\n  name: \"ChainTransferRecord\",\r\n  data () {\r\n    return {\r\n      loading: false,\r\n      searchFrom: {\r\n        keyword: \"\",\r\n        brandCode: \"\",\r\n        page: 1,\r\n        limit: 20,\r\n        total: 0\r\n      },\r\n      tableData: [],\r\n      brandList: []\r\n    };\r\n  },\r\n  created () { },\r\n  mounted () {\r\n    this.getList();\r\n    this.getBrandList();\r\n  },\r\n  methods: {\r\n    formatAmount(s){\r\n        if(s == undefined) {\r\n            s = 0\r\n        }\r\n        let s1 = (s/1000).toFixed(3)\r\n        return s1\r\n    },\r\n    // 列表\r\n    getList (num) {\r\n      this.loading = true;\r\n\r\n      this.searchFrom.page = num ? num : this.searchFrom.page;\r\n      productShareRecordApi(this.searchFrom)\r\n        .then(res => {\r\n          this.tableData = res.list || [];\r\n          this.searchFrom.total = res.total;\r\n          this.loading = false;\r\n        })\r\n        .catch(() => {\r\n          this.loading = false;\r\n        });\r\n    },\r\n\r\n    getBrandList () {\r\n      const params = {\r\n        page: 1,\r\n        limit: 999999,\r\n        name: \"\",\r\n        type: \"-1\"\r\n      };\r\n      brandLstApi(params)\r\n        .then(res => {\r\n          this.brandList = res.list;\r\n        })\r\n        .catch(res => {\r\n          this.$message.error(this.$t(\"common.fetchDataFailed\"));\r\n        });\r\n    },\r\n    //切换页数\r\n    pageChange (index) {\r\n      this.searchFrom.page = index;\r\n      this.getList();\r\n    },\r\n    //切换显示条数\r\n    sizeChange (index) {\r\n      this.searchFrom.limit = index;\r\n      this.getList();\r\n    },\r\n    resetForm () {\r\n      this.searchFrom = {\r\n        keyword: \"\",\r\n        brandCode: \"\",\r\n        page: 1,\r\n        limit: 20,\r\n        total: 0\r\n      };\r\n      this.getList();\r\n    },\r\n\r\n    formatRate (s) {\r\n      return parseInt(s * 10000) / 100 + \"%\";\r\n    }\r\n  }\r\n};\r\n", null]}