{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\operations\\chainTransferRecord\\index.vue?vue&type=template&id=132279b7&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\operations\\chainTransferRecord\\index.vue", "mtime": 1754364854665}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754052682065}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["\n<div class=\"divBox relative\">\n  <el-card class=\"box-card\">\n    <div class=\"container mt-1\">\n      <el-form v-model=\"searchFrom\" inline size=\"small\">\n        <el-form-item :label=\"$t('chainTransferRecord.keyword') + '：'\">\n          <el-input\n            v-model=\"searchFrom.keyword\"\n            :placeholder=\"$t('common.enter')\"\n            clearable\n          ></el-input>\n        </el-form-item>\n        <el-form-item :label=\"$t('chainTransferRecord.brandName') + '：'\">\n          <el-select\n            v-model=\"searchFrom.brandCode\"\n            :placeholder=\"$t('common.all')\"\n            clearable\n          >\n            <el-option\n              v-for=\"item in brandList\"\n              :key=\"item.code\"\n              :label=\"item.name\"\n              :value=\"item.code\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n    </div>\n\n    <el-button size=\"small\" type=\"primary\" class=\"mr10\" @click=\"getList(1)\">\n      {{ $t(\"chainTransferRecord.query\") }}\n    </el-button>\n\n    <el-button size=\"small\" type=\"\" class=\"mr10\" @click=\"resetForm\">\n      {{ $t(\"chainTransferRecord.reset\") }}\n    </el-button>\n  </el-card>\n\n  <el-card class=\"box-card\" style=\"margin-top: 12px\">\n    <div slot=\"header\" class=\"clearfix\">\n      <el-button\n        type=\"primary\"\n        size=\"small\"\n        v-hasPermi=\"['admin:financialCenter:request:upload']\"\n      >\n        {{ $t(\"chainTransferRecord.exportExcel\") }}\n      </el-button>\n    </div>\n    <el-table\n      v-loading=\"loading\"\n      :data=\"tableData\"\n      size=\"small\"\n      :header-cell-style=\"{ fontWeight: 'bold' }\"\n    >\n      <el-table-column\n        :label=\"$t('chainTransferRecord.serialNumber')\"\n        type=\"index\"\n        width=\"110\"\n      />\n      <el-table-column :label=\"$t('chainTransferRecord.nickname')\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.userAccount | filterEmpty }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column :label=\"$t('chainTransferRecord.tiktokId')\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.tiktokUid | filterEmpty }}</span>\n        </template>\n      </el-table-column>\n      <el-table-column :label=\"$t('chainTransferRecord.originalLink')\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.originUrl | filterEmpty }}</span>\n        </template></el-table-column\n      >\n      <el-table-column :label=\"$t('chainTransferRecord.rebateLink')\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.shareUrl | filterEmpty }}</span>\n        </template></el-table-column\n      >\n      <el-table-column :label=\"$t('chainTransferRecord.operationTime')\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.operateTime | filterEmpty }}</span>\n        </template></el-table-column\n      >\n      <el-table-column :label=\"$t('chainTransferRecord.linkSource')\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.channel | filterEmpty }}</span>\n        </template></el-table-column\n      >\n      <el-table-column :label=\"$t('chainTransferRecord.productId')\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.productId | filterEmpty }}</span>\n        </template></el-table-column\n      >\n      <el-table-column :label=\"$t('chainTransferRecord.productName')\">\n        <template slot-scope=\"scope\">\n          <span>{{ scope.row.productName | filterEmpty }}</span>\n        </template></el-table-column\n      >\n      <el-table-column :label=\"$t('chainTransferRecord.productPrice')\">\n        <template slot-scope=\"scope\">\n          <span>{{ formatAmount(scope.row.productPrice) | filterEmpty }}</span>\n        </template></el-table-column\n      >\n      <el-table-column :label=\"$t('chainTransferRecord.productCashbackRate')\">\n        <template slot-scope=\"scope\">{{\n          formatRate(scope.row.productCashbackRate)\n        }}</template></el-table-column\n      >\n    </el-table>\n    <el-pagination\n      class=\"mt20\"\n      @size-change=\"sizeChange\"\n      @current-change=\"pageChange\"\n      :current-page=\"searchFrom.page\"\n      :page-sizes=\"[20, 40, 60, 100]\"\n      :page-size=\"searchFrom.limit\"\n      layout=\"total, sizes, prev, pager, next, jumper\"\n      :total=\"searchFrom.total\"\n    >\n    </el-pagination>\n  </el-card>\n</div>\n", null]}