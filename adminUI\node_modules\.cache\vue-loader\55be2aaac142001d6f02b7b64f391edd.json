{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\brand\\product\\list.vue?vue&type=template&id=f62d8840&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\brand\\product\\list.vue", "mtime": 1754364854658}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754052682065}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox relative\" },\n    [\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _c(\n            \"div\",\n            {\n              staticClass: \"clearfix\",\n              attrs: { slot: \"header\" },\n              slot: \"header\",\n            },\n            [\n              _c(\n                \"div\",\n                { staticClass: \"container mt-1\" },\n                [\n                  _c(\n                    \"el-form\",\n                    { attrs: { inline: \"\", size: \"small\" } },\n                    [\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: _vm.$t(\"product.search\") } },\n                        [\n                          _c(\"el-input\", {\n                            staticClass: \"selWidth\",\n                            attrs: {\n                              placeholder: _vm.$t(\"product.enterProductName\"),\n                              size: \"small\",\n                              clearable: \"\",\n                            },\n                            model: {\n                              value: _vm.form.keywords,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.form, \"keywords\", $$v)\n                              },\n                              expression: \"form.keywords\",\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: _vm.$t(\"product.status\") } },\n                        [\n                          _c(\n                            \"el-select\",\n                            {\n                              attrs: {\n                                placeholder: _vm.$t(\"product.pleaseSelect\"),\n                              },\n                              model: {\n                                value: _vm.form.type,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.form, \"type\", $$v)\n                                },\n                                expression: \"form.type\",\n                              },\n                            },\n                            _vm._l(_vm.statusOptions, function (item) {\n                              return _c(\"el-option\", {\n                                key: item.value,\n                                attrs: {\n                                  label: _vm.$t(\"product.\" + item.label),\n                                  value: item.value,\n                                },\n                              })\n                            }),\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-form-item\",\n                        { attrs: { label: _vm.$t(\"brand.brandName\") } },\n                        [\n                          _c(\n                            \"el-select\",\n                            {\n                              attrs: {\n                                placeholder: _vm.$t(\"product.pleaseSelect\"),\n                                clearable: \"\",\n                              },\n                              model: {\n                                value: _vm.form.brand,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.form, \"brand\", $$v)\n                                },\n                                expression: \"form.brand\",\n                              },\n                            },\n                            _vm._l(_vm.brandOptions, function (item) {\n                              return _c(\"el-option\", {\n                                key: item.value,\n                                attrs: { label: item.label, value: item.value },\n                              })\n                            }),\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-button\",\n                {\n                  staticClass: \"mr10\",\n                  attrs: { size: \"small\", type: \"primary\" },\n                  on: { click: _vm.onSearch },\n                },\n                [\n                  _vm._v(\n                    \"\\n         \" +\n                      _vm._s(_vm.$t(\"product.query\")) +\n                      \"\\n       \"\n                  ),\n                ]\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-button\",\n                {\n                  staticClass: \"mr10\",\n                  attrs: { size: \"small\", type: \"\" },\n                  on: { click: _vm.onReset },\n                },\n                [\n                  _vm._v(\n                    \"\\n         \" +\n                      _vm._s(_vm.$t(\"product.reset\")) +\n                      \"\\n       \"\n                  ),\n                ]\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"div\",\n                { staticClass: \"acea-row padtop-10\" },\n                [\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: { size: \"small\", type: \"success\" },\n                      on: { click: _vm.onAdd },\n                    },\n                    [\n                      _vm._v(\n                        \"\\n           \" +\n                          _vm._s(_vm.$t(\"product.addProduct\")) +\n                          \"\\n         \"\n                      ),\n                    ]\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: { size: \"small\" },\n                      on: {\n                        click: function ($event) {\n                          return _vm.batchHandle(\"online\")\n                        },\n                      },\n                    },\n                    [\n                      _vm._v(\n                        \"\\n           \" +\n                          _vm._s(_vm.$t(\"product.batchOnline\")) +\n                          \"\\n         \"\n                      ),\n                    ]\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: { size: \"small\" },\n                      on: {\n                        click: function ($event) {\n                          return _vm.batchHandle(\"outline\")\n                        },\n                      },\n                    },\n                    [\n                      _vm._v(\n                        \"\\n           \" +\n                          _vm._s(_vm.$t(\"product.batchOffline\")) +\n                          \"\\n         \"\n                      ),\n                    ]\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: { size: \"small\" },\n                      on: {\n                        click: function ($event) {\n                          return _vm.batchHandle(\"delete\")\n                        },\n                      },\n                    },\n                    [\n                      _vm._v(\n                        \"\\n           \" +\n                          _vm._s(_vm.$t(\"product.batchDelete\")) +\n                          \"\\n         \"\n                      ),\n                    ]\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-table\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.listLoading,\n                  expression: \"listLoading\",\n                },\n              ],\n              staticStyle: { width: \"100%\" },\n              attrs: {\n                data: _vm.tableData.data,\n                size: \"mini\",\n                \"highlight-current-row\": true,\n                \"header-cell-style\": { fontWeight: \"bold\" },\n              },\n              on: { \"selection-change\": _vm.handleSelection },\n            },\n            [\n              _c(\"el-table-column\", {\n                attrs: { type: \"selection\", width: \"55\" },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"product.productImage\"),\n                  \"min-width\": \"80\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"div\",\n                          { staticClass: \"demo-image__preview\" },\n                          [\n                            _c(\"el-image\", {\n                              staticStyle: { width: \"36px\", height: \"36px\" },\n                              attrs: {\n                                src: scope.row.image || \"\",\n                                \"preview-src-list\": [scope.row.image || \"\"],\n                              },\n                            }),\n                          ],\n                          1\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"product.productName\"),\n                  \"min-width\": \"160\",\n                  \"show-overflow-tooltip\": true,\n                  prop: \"storeName\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"product.productPrice\"),\n                  \"min-width\": \"90\",\n                  align: \"center\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _vm._v(\n                          \"\\n           \" +\n                            _vm._s(_vm.formatAmount(scope.row.price)) +\n                            \"\\n       \"\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"product.cashbackRate\"),\n                  \"min-width\": \"100\",\n                  align: \"center\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _vm._v(_vm._s(_vm.formatRate(scope.row.cashBackRate))),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"product.isHot\"),\n                  \"min-width\": \"80\",\n                  align: \"center\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"el-switch\", {\n                          attrs: {\n                            \"active-value\": true,\n                            \"inactive-value\": false,\n                          },\n                          on: {\n                            change: function ($event) {\n                              return _vm.isShowChange(\n                                scope.row,\n                                scope.row.isHot,\n                                \"isHot\"\n                              )\n                            },\n                          },\n                          model: {\n                            value: scope.row.isHot,\n                            callback: function ($$v) {\n                              _vm.$set(scope.row, \"isHot\", $$v)\n                            },\n                            expression: \"scope.row.isHot\",\n                          },\n                        }),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"product.isBenefit\"),\n                  \"min-width\": \"80\",\n                  align: \"center\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"el-switch\", {\n                          attrs: {\n                            \"active-value\": true,\n                            \"inactive-value\": false,\n                          },\n                          on: {\n                            change: function ($event) {\n                              return _vm.isShowChange(\n                                scope.row,\n                                scope.row.isBenefit,\n                                \"isBenefit\"\n                              )\n                            },\n                          },\n                          model: {\n                            value: scope.row.isBenefit,\n                            callback: function ($$v) {\n                              _vm.$set(scope.row, \"isBenefit\", $$v)\n                            },\n                            expression: \"scope.row.isBenefit\",\n                          },\n                        }),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"product.isTikTok\"),\n                  \"min-width\": \"80\",\n                  align: \"center\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"el-switch\", {\n                          attrs: {\n                            \"active-value\": true,\n                            \"inactive-value\": false,\n                          },\n                          on: {\n                            change: function ($event) {\n                              return _vm.isShowChange(\n                                scope.row,\n                                scope.row.isBest,\n                                \"isBest\"\n                              )\n                            },\n                          },\n                          model: {\n                            value: scope.row.isBest,\n                            callback: function ($$v) {\n                              _vm.$set(scope.row, \"isBest\", $$v)\n                            },\n                            expression: \"scope.row.isBest\",\n                          },\n                        }),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"product.addTime\"),\n                  \"min-width\": \"120\",\n                  align: \"center\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [_vm._v(_vm._s(_vm.formatTime(scope.row.addTime)))]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"product.online\"),\n                  \"min-width\": \"80\",\n                  align: \"center\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\"el-switch\", {\n                          attrs: {\n                            \"active-value\": true,\n                            \"inactive-value\": false,\n                          },\n                          on: {\n                            change: function ($event) {\n                              return _vm.handleUpdate(scope.row)\n                            },\n                          },\n                          model: {\n                            value: scope.row.isShow,\n                            callback: function ($$v) {\n                              _vm.$set(scope.row, \"isShow\", $$v)\n                            },\n                            expression: \"scope.row.isShow\",\n                          },\n                        }),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"product.action\"),\n                  \"min-width\": \"100\",\n                  fixed: \"right\",\n                  align: \"center\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"el-button\",\n                          {\n                            staticClass: \"mr10\",\n                            attrs: { type: \"text\", size: \"small\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.editProduct(scope.row, scope.$index)\n                              },\n                            },\n                          },\n                          [\n                            _vm._v(\n                              \"\\n             \" +\n                                _vm._s(_vm.$t(\"product.edit\")) +\n                                \"\\n           \"\n                            ),\n                          ]\n                        ),\n                        _vm._v(\" \"),\n                        _c(\n                          \"el-button\",\n                          {\n                            attrs: { type: \"text\", size: \"small\" },\n                            on: {\n                              click: function ($event) {\n                                return _vm.handleDelete(scope.row, scope.$index)\n                              },\n                            },\n                          },\n                          [\n                            _vm._v(\n                              \"\\n             \" +\n                                _vm._s(_vm.$t(\"product.delete\")) +\n                                \"\\n           \"\n                            ),\n                          ]\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            { staticClass: \"block\" },\n            [\n              _c(\"el-pagination\", {\n                attrs: {\n                  \"page-sizes\": [20, 40, 60, 80],\n                  \"page-size\": _vm.form.limit,\n                  \"current-page\": _vm.form.page,\n                  layout: \"total, sizes, prev, pager, next, jumper\",\n                  total: _vm.tableData.total,\n                },\n                on: {\n                  \"size-change\": _vm.handleSizeChange,\n                  \"current-change\": _vm.pageChange,\n                },\n              }),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            title: _vm.$t(\"product.addDialogTitle\"),\n            visible: _vm.productDialogVisible,\n            width: \"540px\",\n            \"before-close\": _vm.handleCloseProductDialog,\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.productDialogVisible = $event\n            },\n          },\n        },\n        [\n          _c(\n            \"el-form\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.loading,\n                  expression: \"loading\",\n                },\n              ],\n              ref: \"dform\",\n              staticClass: \"mt24\",\n              attrs: { model: _vm.dform, \"label-width\": \"160px\" },\n              nativeOn: {\n                submit: function ($event) {\n                  $event.preventDefault()\n                },\n              },\n            },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { label: _vm.$t(\"product.enterProductLink\") } },\n                [\n                  _c(\"el-input\", {\n                    staticClass: \"selWidth width200\",\n                    attrs: {\n                      placeholder: \"please input link\",\n                      size: \"small\",\n                      clearable: \"\",\n                    },\n                    model: {\n                      value: _vm.url,\n                      callback: function ($$v) {\n                        _vm.url = $$v\n                      },\n                      expression: \"url\",\n                    },\n                  }),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: { size: \"small\" },\n                      on: { click: _vm.fetchProduct },\n                    },\n                    [\n                      _vm._v(\n                        \"\\n           \" +\n                          _vm._s(_vm.$t(\"product.fetchProductInfo\")) +\n                          \"\\n         \"\n                      ),\n                    ]\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: _vm.$t(\"brand.brandName\") } },\n                [\n                  _c(\n                    \"div\",\n                    {\n                      staticStyle: {\n                        display: \"flex\",\n                        \"align-items\": \"center\",\n                        gap: \"8px\",\n                      },\n                    },\n                    [\n                      _c(\n                        \"el-select\",\n                        {\n                          staticStyle: { flex: \"1\" },\n                          attrs: {\n                            placeholder: _vm.$t(\"brand.pleaseSelect\"),\n                            loading: _vm.brandLoading,\n                            filterable: \"\",\n                            clearable: \"\",\n                          },\n                          model: {\n                            value: _vm.brandName,\n                            callback: function ($$v) {\n                              _vm.brandName = $$v\n                            },\n                            expression: \"brandName\",\n                          },\n                        },\n                        _vm._l(_vm.brandOptions, function (item) {\n                          return _c(\"el-option\", {\n                            key: item.value,\n                            attrs: { label: item.label, value: item.value },\n                          })\n                        }),\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\"el-button\", {\n                        attrs: {\n                          size: \"small\",\n                          icon: \"el-icon-refresh\",\n                          loading: _vm.brandLoading,\n                          title:\n                            _vm.$t(\"brand.refreshBrands\") || \"刷新品牌数据\",\n                        },\n                        on: { click: _vm.refreshBrands },\n                      }),\n                    ],\n                    1\n                  ),\n                ]\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: _vm.$t(\"product.productName\") } },\n                [\n                  _c(\"el-input\", {\n                    staticClass: \"selWidth readonly-input\",\n                    attrs: {\n                      readonly: \"\",\n                      placeholder: _vm.$t(\"product.enterProductName\"),\n                      size: \"small\",\n                      clearable: \"\",\n                    },\n                    model: {\n                      value: _vm.dform.storeName,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.dform, \"storeName\", $$v)\n                      },\n                      expression: \"dform.storeName\",\n                    },\n                  }),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\"el-form-item\", {\n                attrs: { label: _vm.$t(\"product.productImage\") },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"div\",\n                          { staticClass: \"demo-image__preview\" },\n                          [\n                            _c(\"el-image\", {\n                              staticStyle: { width: \"36px\", height: \"36px\" },\n                              attrs: {\n                                src: _vm.dform.image,\n                                \"preview-src-list\": [_vm.dform.image],\n                              },\n                            }),\n                          ],\n                          1\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: _vm.$t(\"product.productPrice\") } },\n                [\n                  _c(\"el-input\", {\n                    staticClass: \"selWidth readonly-input\",\n                    attrs: {\n                      readonly: \"\",\n                      placeholder: _vm.$t(\"product.enterProductPrice\"),\n                      size: \"small\",\n                      clearable: \"\",\n                    },\n                    model: {\n                      value: _vm.dform.price,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.dform, \"price\", $$v)\n                      },\n                      expression: \"dform.price\",\n                    },\n                  }),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: _vm.$t(\"product.cashbackRate\") } },\n                [\n                  _c(\"el-input\", {\n                    staticClass: \"selWidth readonly-input\",\n                    attrs: {\n                      readonly: \"\",\n                      placeholder: _vm.$t(\"product.enterCashbackRate\"),\n                      size: \"small\",\n                      clearable: \"\",\n                    },\n                    model: {\n                      value: _vm.dform.forMatCashBackRate,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.dform, \"forMatCashBackRate\", $$v)\n                      },\n                      expression: \"dform.forMatCashBackRate\",\n                    },\n                  }),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: _vm.$t(\"product.isOnline\") } },\n                [\n                  _c(\n                    \"el-select\",\n                    {\n                      attrs: { placeholder: _vm.$t(\"product.pleaseSelect\") },\n                      model: {\n                        value: _vm.status,\n                        callback: function ($$v) {\n                          _vm.status = $$v\n                        },\n                        expression: \"status\",\n                      },\n                    },\n                    _vm._l(_vm.typeOptions, function (item) {\n                      return _c(\"el-option\", {\n                        key: item.value,\n                        attrs: {\n                          label: _vm.$t(\"product.\" + item.label),\n                          value: item.value,\n                        },\n                      })\n                    }),\n                    1\n                  ),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"span\",\n            {\n              staticClass: \"dialog-footer\",\n              attrs: { slot: \"footer\" },\n              slot: \"footer\",\n            },\n            [\n              _c(\n                \"el-button\",\n                { attrs: { type: \"primary\" }, on: { click: _vm.onSubProduct } },\n                [_vm._v(_vm._s(_vm.$t(\"product.confirm\")))]\n              ),\n              _vm._v(\" \"),\n              _c(\"el-button\", { on: { click: _vm.handleCloseProductDialog } }, [\n                _vm._v(_vm._s(_vm.$t(\"product.cancel\"))),\n              ]),\n            ],\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}