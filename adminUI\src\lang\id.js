export default {
  common: {
    confirm: "Konfirmasi",
    cancel: "Batal",
    tip: "Tips",
    cancelled: "Dibatalkan",
    deleteFile: "Hapus file ini secara permanen",
    systemTip: "Tips Sistem"
  },
  navbar: {
    home: "<PERSON>randa",
    profile: "Profil",
    logout: "Kelu<PERSON>"
  },
  common: {
    editSuccess: "Berhasil diubah",
    addSuccess: "Berhasil ditambahkan",
    confirmDelete: "Apakah Anda yakin ingin menghapus data bernama '{name}'?",
    status: "Status",
    fetchDataFailed: "Gagal mengambil data",
    operationSuccess: "Operasi berhasil",
    operationFailed: "Operasi gagal",
    unknownError: "Kesalahan Tidak Diketahui",
    confirm: "Konfirmasi",
    cancel: "Bat<PERSON>",
    deleteConfirm: "Yakin hapus?",
    deleteSuccess: "Berhasil dihapus",
    deleteFailed: "Gagal menghapus",
    saveSuccess: "Berhas<PERSON> disimpan",
    saveFailed: "Gagal menyimpan",
    enterRejectReason: "Masukkan alasan penolakan",
    startDate: "Tanggal Mulai",
    endDate: "Tanggal Akhir",
    all: "Semua",
    serialNumber: "Nomor Urut",
    query: "Cari",
    reset: "Reset",
    enter: "Masukkan",
    pendingReview: "Menunggu Review",
    reviewedPassed: "Telah Disetujui",
    reviewedRejected: "Telah Ditolak",
    pleaseSelect: "Silakan Pilih",
    yes: "Ya",
    no: "Tidak",
    show: "Tampilkan",
    hide: "Sembunyikan",
    unknown: "Tidak Dikenal",
    keyword: {
      text: "Pesan Teks",
      image: "Pesan Gambar",
      news: "Pesan Gambar & Teks",
      voice: "Pesan Suara"
    },
    couponType: {
      general: "Kupon Umum",
      product: "Kupon Produk",
      category: "Kupon Kategori"
    },
    couponReceive: {
      manual: "Ambil Manual",
      newUser: "Kupon Pengguna Baru",
      gift: "Kupon Hadiah"
    },
    paymentStatus: {
      unpaid: "Belum Dibayar",
      paid: "Sudah Dibayar"
    },
    withdrawType: {
      bank: "Kartu Bank",
      alipay: "Alipay",
      wechat: "WeChat"
    },
    rechargeType: {
      wechatPublic: "Akun Resmi WeChat",
      wechatH5: "Pembayaran H5 WeChat",
      miniProgram: "Program Mini"
    },
    withdrawStatus: {
      rejected: "Ditolak",
      reviewing: "Dalam Tinjauan",
      withdrawn: "Telah Dicairkan"
    },
    status: {
      bargain: {
        1: "Berlangsung",
        2: "Belum Selesai",
        3: "Berhasil"
      }
    },
    onePass: {
      sms: "SMS",
      copy: "Pengumpulan Produk",
      expr_query: "Inquiry Logistik",
      expr_dump: "Cetak Resi Elektronik"
    },
    editStatus: {
      1: "Belum Diperiksa",
      2: "Dalam Tinjauan",
      3: "Tinjauan Gagal",
      4: "Berhasil Diperiksa"
    },
    videoStatus: {
      0: "Nilai Awal",
      5: "Diterbitkan",
      11: "Ditarik Sendiri",
      13: "Dihapus karena Pelanggaran / Dihapus Sistem Risiko"
    }
  },
  appMain: {
    copyright: "Hak Cipta © 2025"
  },
  dashboard: {
    home: "Beranda",
    brandCenter: "Pusat Merek",
    brandManage: "Manajemen Merek",
    productManage: "Manajemen Produk",
    appManage: "Manajemen Aplikasi",
    homeManage: "Manajemen Halaman Utama",
    opsCenter: "Pusat Operasi",
    affiliateProducts: "Produk Afiliasi",
    withdrawalReview: "Pemeriksaan Penarikan",
    withdrawalRecords: "Catatan Penarikan",
    orderCenter: "Pusat Pesanan",
    orderInquiry: "Pencarian Pesanan",
    userCenter: "Pusat Pengguna",
    userManage: "Manajemen Pengguna",
    userLevel: "Level Pengguna",
    levelUpgradeOrder: "Pesanan Peningkatan Level",
    financeCenter: "Pusat Keuangan",
    financeDetails: "Rincian Keuangan",
    withdrawalRequest: "Permintaan Penarikan",
    paramSettings: "Pengaturan Parameter",
    rewardRules: "Pengaturan Aturan Hadiah",
    withdrawalFee: "Pengaturan Biaya Penarikan",
    referralRewardConfig: "Konfigurasi Hadiah Rujukan",
    membershipFee: "Pengaturan Biaya Peningkatan Keanggotaan",
    accountCenter: "Pusat Akun",
    adminPermissions: "Izin Administrasi",
    roleManage: "Manajemen Peran",
    adminList: "Daftar Administrator",
    permissionRules: "Aturan Izin",
    profile: "Profil",
    systemSettings: "Pengaturan Sistem",
    chainTransferRecord: "Rekam beralih rantai",
    platformCashbackRate: "Pengaturan Tingkat Pengembalian Platform",
    shoppingCashbackRules: "Aturan Cashback Belanja"
  },
  platformCashbackRate: {
    platformCashbackRate: "Platform Cashback Rate",
    editTitle: "Edit Platform Cashback Rate",
    addTitle: "Add Platform Cashback Rate",
    placeholder: {
      platformCashbackRate: "Please enter platform cashback rate"
    }
  },
  tagsView: {
    refresh: "Segarkan",
    close: "Tutup",
    closeOthers: "Tutup Lainnya",
    closeAll: "Tutup Semua"
  },
  homepage: {
    welcome: "Selamat datang di Panel Admin GENCO!",
    paymentSwitch: "Pengaturan Pembayaran",
    paymentSwitchTip1:
      "Jika diaktifkan, frontend mengizinkan pengguna menjadi agen dan mitra.",
    paymentSwitchTip2:
      "Jika dinonaktifkan, peningkatan tidak diperbolehkan. Pengaturan ini hanya untuk",
    paymentSwitchTip3:
      "kemudahan pengajuan ke AppStore. Jangan diubah sembarangan.",
    loginMode: "Metode Login",
    loginModeTip1:
      "Pengaturan ini hanya mengontrol tampilan login TikTok dan login SMS",
    loginModeTip2:
      "di halaman login untuk kemudahan pengajuan ke AppStore. Jangan diubah sembarangan.",
    tikTokLogin: "Login TikTok",
    smsLogin: "Login SMS",
    submit: "Simpan"
  },
  brand: {
    search: "Cari Merek:",
    status: "Status:",
    pleaseSelect: "Silakan Pilih",
    reset: "Reset",
    query: "Cari",
    addBrand: "Tambah Merek",
    batchOnline: "Aktifkan Semua",
    batchOffline: "Nonaktifkan Semua",
    batchDelete: "Hapus Semua",
    brandLogo: "Logo Merek",
    brandName: "Nama Merek",
    industry: "Industri",
    platform: "Platform",
    productCount: "Jumlah Produk",
    maxCashback: "Rate Cashback Tertinggi",
    soldCount: "Jumlah Produk Terjual",
    soldAmount: "Jumlah Penjualan (Rp)",
    cashbackAmount: "Jumlah Cashback (Rp)",
    shareCount: "Jumlah Bagi",
    createTime: "Waktu Dibuat",
    creator: "Pembuat",
    statusLabel: "Status",
    isHot: "Apakah Merek Populer",
    isHighCashback: "Apakah Merek Cashback Tinggi",
    offline: "Nonaktif",
    online: "Aktif",
    edit: "Ubah",
    delete: "Hapus",
    addDialogTitle: "Tambah Merek",
    editDialogTitle: "Ubah Merek",
    brandNameInput: "Masukkan nama merek",
    brandLogoInput: "Masukkan URL gambar",
    contactPerson: "Kontak:",
    contactPhone: "Telepon Kontak:",
    confirm: "Simpan",
    update: "Perbarui",
    cancel: "Batal",
    platformTiktok: "TikTok",
    platformShopee: "Shopee",
    confirmOperation: "Apakah Anda yakin ingin melakukan operasi ini?",
    prompt: "Peringatan",
    productList: "Product",
    isOnline: "On Sale",
    isOutline: "Pending",
    isOuted: "Offline",
    selectTip: "Silakan pilih",
    refreshBrands: "Segarkan Data Merek",
    refreshingBrands: "Menyegarkan data merek..."
  },
  product: {
    search: "Cari Produk:",
    keywordsPlaceholder: "Masukkan nama atau kata kunci produk",
    status: "Status:",
    pleaseSelect: "Silakan Pilih",
    query: "Cari",
    reset: "Reset",
    addProduct: "Tambah Produk",
    batchOnline: "Aktifkan Semua",
    batchOffline: "Nonaktifkan Semua",
    batchDelete: "Hapus Semua",
    productImage: "Gambar Produk",
    productName: "Nama Produk",
    productPrice: "Harga Produk",
    cashbackRate: "Rate Cashback",
    estimatedCashback: "Jumlah Cashback (Rp)",
    productLink: "Tautan Produk",
    shareCount: "Jumlah Bagi",
    soldCount: "Jumlah Terjual",
    cashbackAmount: "Jumlah Cashback (Rp)",
    addTime: "Waktu Penambahan",
    action: "Aksi",
    offline: "Nonaktif",
    online: "Aktif",
    edit: "Ubah",
    delete: "Hapus",
    isHot: "Populer",
    isBenefit: "Cashback Tinggi",
    isTikTok: "TikTok",
    addDialogTitle: "Tambah Produk",
    enterProductLink: "Masukkan tautan produk",
    fetchProductInfo: "Ambil Info Produk",
    enterProductName: "Masukkan nama produk",
    productPrice: "Harga Produk",
    enterProductPrice: "Masukkan harga produk",
    // cashbackRate: "Rate Cashback",
    enterCashbackRate: "Masukkan rate cashback",
    // estimatedCashback: "Jumlah Cashback",
    enterCashbackAmount: "Masukkan jumlah cashback",
    isOnline: "Apakah Aktif:",
    yes: "Ya",
    no: "Tidak",
    confirm: "Simpan",
    cancel: "Batal",
    all: "Semua",
    fetchProductFailed: "Gagal mengambil info produk",
    isOnIndex: "Tampilkan di beranda?",
    usercashbackRate: "User Cashback Rate",
    isOnline: "On Sale",
    isOutline: "Pending",
    isOuted: "Offline"
  },
  operations: {
    withdrawal: {
      walletWithdrawal: "Penarikan Dompet Elektronik",
      bankWithdrawal: "Penarikan Bank",
      applicant: "Pemohon",
      applicationTime: "Waktu Pengajuan",
      electronicWallet: "Dompet Elektronik",
      bankName: "Nama Bank",
      applicationId: "ID Pengajuan",
      applicantName: "Pemohon",
      withdrawalAmount: "Jumlah Penarikan",
      serviceFee: "Biaya Layanan",
      actualAmount: "Jumlah yang Diterima",
      walletCode: "Kode Dompet",
      walletAccount: "Akun",
      bankCardNumber: "Nomor Kartu Bank",
      name: "Nama",
      phoneNumber: "Nomor Telepon",
      withdrawalCount: "Jumlah Riwayat Penarikan",
      auditResult: "Hasil Audit",
      rejectReason: "Alasan Penolakan",
      approve: "Setujui",
      reject: "Tolak",
      rejectReview: "Tolak Audit",
      exportExcel: "Ekspor ke Excel",
      transferTime: "Waktu Transfer",
      transferResult: "Hasil Transfer",
      remark: "Catatan",
      attachment: "Lampiran",
      operator: "Operator",
      withdrawalStatus: "Status Penarikan",
      ShopeePay: "ShopeePay",
      DANA: "DANA",
      OVO: "OVO",
      Gopay: "Gopay",
      unapproved: "Tidak Disetujui",
      underReview: "Dalam Tinjauan",
      reviewed: "Telah Diperiksa",
      paid: "Telah Dibayarkan"
    }
  },
  order: {
    search: {
      orderNo: "Order Number",
      productTitle: "Product Name",
      status: "Status",
      all: "All",
      query: "Query",
      reset: "Reset",
      exportExcel: "Export Excel",
      serialNumber: "Serial Number",
      productImage: "Product Image",
      orderId: "Order ID",
      productName: "Product Name",
      payCount: "Purchase Count",
      actualCommission: "Product Price (Rp)",
      payPrice: "Order Amount (Rp)",
      commissionRate: "Product Cashback Rate",
      estimatedCommission: "Estimated Cashback (Rp)",
      contentId: "E-commerce Platform",
      statusLabel: "Order Status",
      unknown: "Unknown",
      ordered: "Ordered",
      settled: "Settled",
      refunded: "Refunded",
      frozen: "Frozen",
      deducted: "Deducted",
      totalPrice: "Jumlah",
      userCashBackRate: "Tingkat Cashback Pengguna",
      creatTime: "Waktu Pemesanan"
    }
  },
  user: {
    center: {
      nickname: "Nama Panggilan",
      phone: "Nomor Telepon",
      userLevel: "Level Pengguna",
      query: "Cari",
      reset: "Reset",
      serialNumber: "Nomor Urut",
      avatar: "Avatar",
      tiktokAccount: "Panggilan TikTok",
      tiktokId: "ID TikTok",
      whatsApp: "Nomor WhatsApp",
      registerTime: "Waktu Pendaftaran",
      lastLoginTime: "Waktu Login Terakhir",
      orderCount: "Jumlah Pesanan",
      orderFinishCount: "Jumlah Pesanan Selesai",
      isAgent: "Apakah Agen",
      isPartner: "Apakah Mitra",
      userLevelLabel: "Level Pengguna",
      inviter: "Pengundang",
      userTags: "Tag Pengguna"
    },
    levelUpgrade: {
      title: "Manajemen Pesanan Peningkatan Level",
      orderNo: "Nomor Pesanan",
      userId: "ID Pengguna",
      upgradeInfo: "Info Peningkatan",
      upgradeFee: "Biaya Peningkatan",
      paymentMethod: "Metode Pembayaran",
      orderStatus: "Status Pesanan",
      createTime: "Waktu Dibuat",
      payTime: "Waktu Pembayaran",
      operation: "Operasi",
      enterOrderNo: "Masukkan nomor pesanan",
      selectStatus: "Pilih status",
      pending: "Menunggu Pembayaran",
      paid: "Sudah Dibayar",
      cancelled: "Dibatalkan",
      refunded: "Dikembalikan",
      cancelOrder: "Batalkan Pesanan",
      viewDetail: "Lihat Detail",
      orderDetail: "Detail Pesanan",
      fromLevel: "Level Asal",
      toLevel: "Level Tujuan",
      remark: "Catatan",
      noRemark: "Tidak ada",
      unpaid: "Belum Dibayar",
      confirmCancel: "Apakah Anda yakin ingin membatalkan pesanan ini?",
      cancelSuccess: "Pesanan berhasil dibatalkan",
      cancelFailed: "Gagal membatalkan pesanan",
      getListFailed: "Gagal mendapatkan daftar pesanan",
      balancePayment: "Pembayaran Saldo",
      unknownLevel: "Level Tidak Dikenal",
      unknownStatus: "Status Tidak Dikenal",
      changeWarning: "Jangan sering mengubah untuk menghindari kebingungan perhitungan!",
      deductExperience: "Kurangi Pengalaman",
      levelNames: {
        1: "Pengguna Biasa",
        2: "Pengguna Silver",
        3: "Pengguna Emas",
        4: "Pengguna Berlian",
        5: "Pengguna Raja",
        6: "Pengguna Master"
      }
    },
    grade: {
      title: "Level Pengguna",
      levelName: "Nama Level",
      experience: "Pengalaman",
      discount: "Diskon",
      commissionRate: "Tingkat Komisi",
      upgradeType: "Jenis Peningkatan",
      upgradeFee: "Biaya Peningkatan",
      availableStatus: "Status Ketersediaan",
      status: "Status",
      operation: "Operasi",
      available: "Tersedia",
      unavailable: "Tidak Tersedia",
      free: "Gratis",
      addUserLevel: "Tambah Level Pengguna",
      levelIcon: "Ikon Level",
      enable: "Aktifkan",
      disable: "Nonaktifkan",
      edit: "Edit",
      delete: "Hapus",
      deleteConfirm: "Yakin ingin menghapus? Ini akan menghapus data level pengguna terkait, harap berhati-hati!",
      deleteSuccess: "Berhasil dihapus",
      updateSuccess: "Berhasil diperbarui",
      hideConfirm: "Operasi ini akan menyembunyikan level pengguna terkait, harap berhati-hati",
      userTypes: {
        wechat: "Pengguna WeChat",
        routine: "Pengguna Mini Program",
        h5: "Pengguna H5"
      },
      upgradeTypes: {
        0: "Daftar Langsung",
        1: "Pembelian Berbayar",
        2: "Aplikasi Offline",
        3: "Kemitraan Saluran"
      },
      form: {
        dialogTitle: "Level Pengguna",
        levelNameLabel: "Nama Level",
        levelNamePlaceholder: "Masukkan nama level",
        gradeLabel: "Grade",
        gradePlaceholder: "Masukkan grade",
        discountLabel: "Diskon (%)",
        discountPlaceholder: "Masukkan diskon",
        experienceLabel: "Pengalaman",
        experiencePlaceholder: "Masukkan pengalaman",
        iconLabel: "Ikon",
        cancel: "Batal",
        confirm: "Konfirmasi",
        editSuccess: "Edit berhasil",
        addSuccess: "Tambah berhasil",
        validation: {
          levelNameRequired: "Masukkan nama level",
          gradeRequired: "Masukkan grade",
          gradeNumber: "Grade harus berupa angka",
          discountRequired: "Masukkan diskon",
          experienceRequired: "Masukkan pengalaman",
          experienceNumber: "Pengalaman harus berupa angka",
          iconRequired: "Unggah ikon",
          imageRequired: "Unggah latar belakang pengguna"
        }
      }
    }
  },
  financial: {
    detail: {
      title: "Rincian Keuangan",
      purchaseDetail: "Rincian Pembelian Anggota",
      tradeDetail: "Rincian Transaksi",
      rechargeType: "Nama Produk",
      transactionTime: "Waktu Transaksi",
      paymentMethod: "Metode Pembayaran",
      electronicWallet: "Dompet Elektronik",
      bankName: "Nama Bank",
      serialNumber: "Nomor Urut",
      paymentTime: "Waktu Pembayaran",
      paymentNo: "Nomor Pembayaran",
      actualPaymentAmount: "Jumlah Pembayaran Aktual",
      institutionNumber: "Nomor Institusi",
      paymentAccount: "Akun Pembayaran",
      mobile: "Nomor Telepon",
      payee: "Penerima",
      payeeAccount: "Akun Penerima",
      tradeNo: "Nomor Transaksi",
      tradeType: "Tipe Transaksi",
      tradeAmount: "Jumlah Transaksi (Rp)",
      userNickname: "Nama Panggilan Pengguna",
      tikTokAccount: "Akun TikTok",
      whatsApp: "WhatsApp",
      channel: "Saluran",
      orderNo: "Nomor Pesanan",
      bankTransfer: "Transfer Bank",
      electronicWallet: "Dompet Elektronik",
      agentFee: "Biaya Agen",
      partnerFee: "Biaya Mitra",
      exportExcel: "Ekspor ke Excel",
      ShopeePay: "ShopeePay",
      DANA: "DANA",
      OVO: "OVO",
      Gopay: "Gopay"
    },
    request: {
      walletWithdrawal: "Penarikan Dompet Elektronik",
      bankWithdrawal: "Penarikan Bank",
      applicant: "Pemohon",
      applicationTime: "Waktu Pengajuan",
      electronicWallet: "Dompet Elektronik",
      bankName: "Nama Bank",
      serialNumber: "Nomor Urut",
      applicationId: "ID Pengajuan",
      applicantName: "Pemohon",
      withdrawalAmount: "Jumlah Penarikan",
      serviceFee: "Biaya Layanan",
      actualAmount: "Jumlah yang Diterima",
      applicationTime: "Waktu Pengajuan",
      walletCode: "Dompet Elektronik",
      walletAccount: "Akun",
      bankCardNumber: "Nomor Kartu Bank",
      name: "Nama",
      phoneNumber: "Nomor Telepon",
      action: "Aksi",
      transferComplete: "Transfer Selesai",
      attachment: "Lampiran",
      remark: "Catatan",
      confirm: "Konfirmasi",
      cancel: "Batal",
      exportExcel: "Ekspor ke Excel"
    },
    history: {
      walletWithdrawal: "Penarikan Dompet Elektronik",
      bankWithdrawal: "Penarikan Bank",
      applicant: "Pemohon",
      applicationTime: "Waktu Pengajuan",
      electronicWallet: "Dompet Elektronik",
      bankName: "Nama Bank",
      serialNumber: "Nomor Urut",
      applicationId: "ID Pengajuan",
      applicantName: "Pemohon",
      withdrawalAmount: "Jumlah Penarikan",
      serviceFee: "Biaya Layanan",
      actualAmount: "Jumlah yang Diterima",
      applicationTime: "Waktu Pengajuan",
      walletCode: "Dompet Elektronik",
      walletAccount: "Akun",
      bankCardNumber: "Nomor Kartu Bank",
      name: "Nama",
      phoneNumber: "Nomor Telepon",
      transferTime: "Waktu Transfer",
      transferResult: "Hasil Transfer",
      remark: "Catatan",
      attachment: "Lampiran",
      operator: "Operator",
      exportExcel: "Ekspor ke Excel",
      status: "Status Penarikan"
    }
  },
  parameter: {
    rewardRules: {
      title: "Pengaturan Aturan Hadiah",
      rewardTemplateName: "Nama Template",
      rewardTemplateId: "ID Template Aturan Hadiah",
      directInviteReward: "Hadiah Langsung per Orang",
      secondLevelInviteReward: "Hadiah Tidak Langsung (Level 2) per Orang (Rp)",
      thirdLevelInviteReward: "Hadiah Tidak Langsung (Level 3) per Orang (Rp)",
      goldRewardPer10: "Hadiah Emas per 10 Orang (Rp)",
      diamondRewardPer10: "Hadiah Berlian per 10 Orang (Rp)",
      operation: "Operasi",
      edit: "Ubah",
      editTitle: "Ubah Pengaturan Aturan Hadiah",
      directAgentLabel: "Undangan langsung adalah agen",
      directPartnerLabel: "Undangan langsung adalah mitra",
      indirectAgent2LevelLabel:
        "Undangan tidak langsung (level 2 adalah agen) setiap orang bisa dapat (Rp)",
      indirectPartner2LevelLabel:
        "Undangan tidak langsung (level 2 adalah mitra) setiap orang bisa dapat (Rp)",
      indirectAgent3LevelLabel:
        "Undangan tidak langsung (level 3 adalah agen) setiap orang bisa dapat (Rp)",
      indirectPartner3LevelLabel:
        "Undangan tidak langsung (level 3 adalah mitra) setiap orang bisa dapat (Rp)"
    },
    withdrawalFee: {
      title: "Pengaturan Biaya Penarikan",
      feeTemplateId: "ID Template Biaya",
      minWithdrawAmount: "Jumlah Penarikan Minimum (Rp)",
      maxWithdrawAmount: "Jumlah Penarikan Maksimum (Rp)",
      withdrawFeeRate: "Tarif Biaya Penarikan (%)",
      operation: "Operasi",
      edit: "Ubah",
      addTitle: "Tambah Aturan Biaya",
      editTitle: "Ubah Aturan Biaya",
      placeholder: {
        couponId: "Masukkan ID kupon",
        minWithdrawAmount: "Masukkan jumlah penarikan minimum",
        maxWithdrawAmount: "Masukkan jumlah penarikan maksimum",
        withdrawFeeRate: "Masukkan tarif biaya penarikan"
      }
    },
    membershipFee: {
      title: "Pengaturan Biaya Peningkatan Keanggotaan",
      feeTemplateId: "ID Template Biaya",
      agentFee: "Biaya Agen (Rp)",
      partnerFee: "Biaya Mitra (Rp)",
      operation: "Operasi",
      edit: "Ubah",
      addTitle: "Tambah Biaya Peningkatan Keanggotaan",
      editTitle: "Ubah Biaya Peningkatan Keanggotaan",
      placeholder: {
        agentFee: "Masukkan biaya agen",
        partnerFee: "Masukkan biaya mitra"
      }
    },
    shoppingCashbackRules: {
      directCashbackRate: "Tingkat Cashback Langsung (%)",
      secondLevelCashbackRate: "Tingkat Cashback Level 2 (%)",
      thirdLevelCashbackRate: "Tingkat Cashback Level 3 (%)",
      normalUserRule: "Aturan Cashback Pengguna Normal",
      agentTeamRule: "Aturan Cashback Tim Agen",
      partnerTeamRule: "Aturan Cashback Tim Mitra"
    },
    referralRewardConfig: {
      title: "Konfigurasi Hadiah Rujukan",
      rewardTemplateId: "ID Template Aturan Hadiah",
      rewardTemplateName: "Nama Template",
      referralCount: "Jumlah Rujukan",
      firstOrderCount: "Jumlah Pesanan Pertama",
      rewardAmount: "Jumlah Hadiah (Rp)",
      rewardRuleZh: "Aturan Hadiah (Cina)",
      rewardRuleEn: "Aturan Hadiah (Inggris)",
      rewardRuleId: "Aturan Hadiah (Indonesia)",
      operation: "Operasi",
      edit: "Ubah",
      editTitle: "Ubah Konfigurasi Hadiah Rujukan",
      basicConfig: "Konfigurasi Dasar",
      validation: {
        referralCountMin: "Jumlah rujukan harus lebih besar atau sama dengan 0",
        firstOrderCountMin: "Jumlah pesanan pertama harus lebih besar atau sama dengan 0",
        firstOrderCountMax: "Jumlah pesanan pertama harus kurang dari jumlah rujukan",
        rewardAmountMin: "Jumlah hadiah harus lebih besar atau sama dengan 0"
      }
    }
  },
  admin: {
    system: {
      role: {
        roleName: "Nama Peran",
        roleId: "ID Peran",
        status: "Status",
        createTime: "Waktu Dibuat",
        updateTime: "Waktu Diperbarui",
        operation: "Operasi",
        addRole: "Tambah Peran",
        editRole: "Ubah Peran",
        deleteRole: "Hapus Peran",
        confirmDelete: "Yakin hapus data ini?",
        deleteSuccess: "Data berhasil dihapus",
        createIdentity: "Buat Identitas",
        editIdentity: "Ubah Identitas",
        roleForm: {
          roleNameLabel: "Nama Peran",
          roleNamePlaceholder: "Nama Identitas",
          statusLabel: "Status",
          menuPermissions: "Izin Menu",
          expandCollapse: "Ekspansi/Kolaps",
          selectAll: "Pilih Semua/Tidak",
          parentChildLink: "Kaitan Induk-Anak",
          confirm: "Konfirmasi",
          update: "Perbarui",
          cancel: "Batal"
        }
      },
      admin: {
        role: "Peran",
        status: "Status",
        realName: "Nama atau Akun",
        id: "ID",
        account: "Akun",
        phone: "Nomor Telepon",
        lastTime: "Waktu Login Terakhir",
        lastIp: "IP Login Terakhir",
        isSms: "Terima SMS",
        isDel: "Tanda Hapus",
        operation: "Operasi",
        addAdmin: "Tambah Admin",
        edit: "Ubah",
        delete: "Hapus",
        createIdentity: "Buat Identitas",
        editIdentity: "Ubah Identitas",
        pleaseAddPhone:
          "Silakan tambahkan nomor telepon untuk admin terlebih dahulu!",
        confirmDelete: "Yakin hapus data ini?",
        deleteSuccess: "Data berhasil dihapus",
        account: "Akun",
        pwd: "Kata Sandi",
        repwd: "Konfirmasi Kata Sandi",
        realName: "Nama",
        roles: "Peran",
        phone: "Nomor Telepon",
        pleaseAddPhone:
          "Silakan tambahkan nomor telepon untuk admin terlebih dahulu!",
        validatePhone: {
          required: "Silakan masukkan nomor telepon",
          formatError: "Format nomor telepon tidak benar!"
        },
        validatePass: {
          required: "Silakan masukkan ulang kata sandi",
          notMatch: "Kata sandi tidak cocok!"
        },
        message: {
          createSuccess: "Admin berhasil dibuat",
          updateSuccess: "Admin berhasil diperbarui"
        },
        validateAccount: {
          required: "Silakan masukkan akun admin"
        },
        validatePassword: {
          required: "Silakan masukkan kata sandi admin"
        },
        validateConfirmPassword: {
          required: "Silakan konfirmasi kata sandi"
        },
        validateRealName: {
          required: "Silakan masukkan nama admin"
        },
        validateRoles: {
          required: "Silakan pilih peran admin"
        },
        validatePassword: {
          required: "Silakan masukkan kata sandi admin",
          lengthError: "Panjang kata sandi harus 6-20 karakter"
        },
        validateConfirmPassword: {
          required: "Silakan konfirmasi kata sandi"
        },
        validatePass: {
          notMatch: "Kata sandi tidak cocok"
        }
      }
    }
  },
  permissionRules: {
    menuName: "Nama Menu",
    status: "Status",
    select: "Silakan Pilih",
    add: "Tambah",
    expandCollapse: "Ekspansi/Kolaps",
    actions: {
      edit: "Ubah",
      add: "Tambah",
      delete: "Hapus"
    },
    table: {
      menuName: "Nama Menu",
      icon: "Ikon",
      sort: "Urutan",
      perm: "Karakter Hak",
      component: "Jalur Komponen",
      status: "Status",
      createTime: "Waktu Buat",
      type: "Jenis"
    },
    menuType: {
      directory: "Direktori",
      menu: "Menu",
      button: "Tombol"
    },
    form: {
      parentMenu: "Menu Induk",
      menuType: "Jenis Menu",
      menuIcon: "Ikon Menu",
      menuName: "Nama Menu",
      sort: "Urutan Tampil",
      component: "Jalur Komponen",
      componentTip:
        "Jalur komponen, contoh: `system/user/index`, default di folder `views`",
      perm: "Karakter Hak",
      permTip:
        'Karakter hak yang didefinisikan di controller, contoh: @PreAuthorize(`@ss.hasPermi("system:user:list")`)',
      showStatus: "Status Tampil",
      showStatusTip:
        "Jika disembunyikan, menu tidak akan muncul di sidebar namun tetap bisa diakses",
      enterMenuName: "Masukkan nama menu",
      enterComponent: "Masukkan jalur komponen",
      enterPerm: "Masukkan karakter hak",
      selectIcon: "Silakan pilih ikon menu",
      selectParentMenu: "Pilih menu induk",
      sortRequired: "Urutan tampil tidak boleh kosong"
    }
  },
  affiliateProducts: {
    title: "Produk Afiliasi",
    keywords: "Kata Kunci:",
    keywordsPlaceholder: "Masukkan kata kunci produk",
    priceRange: "Rentang Harga:",
    minPrice: "Harga Minimum",
    maxPrice: "Harga Maksimum",
    commissionRange: "Rentang Tingkat Komisi:",
    minCommission: "Tingkat Komisi Minimum (%)",
    maxCommission: "Tingkat Komisi Maksimum (%)",
    sort: "Urutkan:",
    sortCommissionRate: "Tingkat Komisi",
    sortCommission: "Jumlah Komisi",
    sortPrice: "Harga Produk",
    sortSales: "Penjualan",
    sortDesc: "Menurun",
    sortAsc: "Menaik",
    query: "Cari",
    reset: "Reset",
    refresh: "Segarkan",
    listTitle: "Daftar Produk Afiliasi",
    batchImport: "Impor Massal",
    batchImporting: "Mengimpor Massal...",
    batchDelete: "Hapus Massal",
    batchDeleting: "Menghapus Massal...",
    emptyTip: "Klik tombol cari untuk mulai mencari produk",
    serialNumber: "Nomor Urut",
    productImage: "Gambar Produk",
    productTitle: "Judul Produk",
    shop: "Toko",
    originalPrice: "Harga Asli",
    salesPrice: "Harga Jual",
    commissionRate: "Tingkat Komisi",
    commissionAmount: "Jumlah Komisi",
    unitsSold: "Unit Terjual",
    inventoryStatus: "Status Inventori",
    hasInventory: "Tersedia",
    noInventory: "Habis",
    saleRegion: "Wilayah Penjualan",
    importStatus: "Status Impor",
    imported: "Telah Diimpor",
    notImported: "Belum Diimpor",
    action: "Aksi",
    import: "Impor Produk",
    importing: "Mengimpor...",
    imported: "Telah Diimpor",
    delete: "Hapus",
    deleting: "Menghapus...",
    prevPage: "Halaman Sebelumnya",
    nextPage: "Halaman Berikutnya",
    pageSize: "Item per halaman:",
    totalCount: "Total {count} produk",
    importSingle: "Impor Produk Tunggal",
    importBatch: "Impor Produk Massal",
    selectedCount: "Produk Terpilih:",
    brandAutoDetect: "Informasi merek akan otomatis diidentifikasi dari data produk TikTok, tidak perlu pemilihan manual",
    confirmImport: "Konfirmasi Impor",
    cancel: "Batal",
    deleteConfirm: "Apakah Anda yakin ingin menghapus produk ini?",
    batchDeleteConfirm: "Apakah Anda yakin ingin menghapus {count} produk secara massal?",
    deleteSuccess: "Berhasil dihapus",
    batchDeleteSuccess: "Berhasil menghapus secara massal",
    importSuccess: "Produk berhasil diimpor!",
    batchImportSuccess: "Impor massal selesai! Berhasil mengimpor {count} produk",
    importExists: "Produk sudah ada, tidak perlu diimpor lagi",
    batchImportExists: "Semua produk sudah ada, tidak perlu diimpor lagi",
    batchImportPartial: "Impor massal sebagian berhasil! Berhasil: {success}, Gagal: {failed}, Dilewati: {skipped}",
    batchImportMixed: "Impor massal selesai! Berhasil: {success}, Dilewati (sudah ada): {skipped}",
    importFailed: "Impor produk gagal! Alasan: {reason}",
    batchImportFailed: "Impor massal gagal! Gagal: {failed}, Dilewati: {skipped}",
    selectFirst: "Silakan pilih produk yang akan diimpor terlebih dahulu",
    selectDeleteFirst: "Silakan pilih produk yang akan dihapus terlebih dahulu",
    searchFirst: "Silakan klik tombol cari terlebih dahulu",
    noResults: "Tidak ditemukan produk yang sesuai kriteria"
  },
  chainTransferRecord: {
    title: "Rekam Beralih Rantai",
    keyword: "Kata Kunci",
    brandName: "Nama Merek",
    query: "Cari",
    reset: "Reset",
    exportExcel: "Ekspor ke Excel",
    serialNumber: "Nomor Urut",
    nickname: "Nama Panggilan",
    tiktokId: "ID TikTok",
    originalLink: "Tautan Asli",
    rebateLink: "Tautan Rebate Setelah Beralih Rantai",
    operationTime: "Waktu Operasi",
    linkSource: "Sumber Tautan",
    productId: "ID Produk",
    productName: "Nama Produk",
    productPrice: "Harga Produk",
    productCashbackRate: "Tingkat Cashback Produk",
    userCashbackRate: "Tingkat Cashback Pengguna"
  },
  message: {
    hello: "Halo",
    userNotice: "Pemberitahuan Pengguna",
    userDetails: {
      balance: "Saldo",
      allOrderCount: "Total Pesanan",
      allConsumeCount: "Total Konsumsi",
      integralCount: "Poin",
      mothOrderCount: "Pesanan Bulan Ini",
      mothConsumeCount: "Konsumsi Bulan Ini",
      consumeRecord: "Catatan Konsumsi",
      integralDetail: "Detail Poin",
      signInRecord: "Catatan Masuk",
      coupons: "Kupon Dimiliki",
      balanceChange: "Perubahan Saldo",
      friendRelation: "Relasi Teman",
      sourceOrPurpose: "Sumber/Tujuan",
      integralChange: "Perubahan Poin",
      balanceAfterChange: "Poin Setelah Perubahan",
      date: "Tanggal",
      remark: "Catatan",
      orderId: "ID Pesanan",
      receiver: "Penerima",
      goodsNum: "Jumlah Barang",
      goodsTotalPrice: "Total Harga Barang",
      payPrice: "Jumlah Dibayar",
      payTime: "Waktu Transaksi",
      action: "Aksi",
      getIntegral: "Poin Didapat",
      signTime: "Waktu Masuk",
      couponName: "Nama Kupon",
      faceValue: "Nilai Kupon",
      validity: "Masa Berlaku",
      minPrice: "Konsumsi Minimum",
      exchangeTime: "Waktu Penukaran",
      changeAmount: "Jumlah Perubahan",
      afterChange: "Setelah Perubahan",
      type: "Tipe",
      createTime: "Waktu Buat",
      id: "ID",
      nickname: "Nama Panggilan",
      level: "Level",
      joinTime: "Waktu Bergabung"
    }
  }
};
