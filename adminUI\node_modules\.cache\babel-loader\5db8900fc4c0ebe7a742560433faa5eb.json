{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\operations\\chainTransferRecord\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\operations\\chainTransferRecord\\index.vue", "mtime": 1754364854665}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _chainTransferRecord = require(\"@/api/chainTransferRecord\");\nvar _brand = require(\"@/api/brand\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: \"ChainTransferRecord\",\n  data: function data() {\n    return {\n      loading: false,\n      searchFrom: {\n        keyword: \"\",\n        brandCode: \"\",\n        page: 1,\n        limit: 20,\n        total: 0\n      },\n      tableData: [],\n      brandList: []\n    };\n  },\n  created: function created() {},\n  mounted: function mounted() {\n    this.getList();\n    this.getBrandList();\n  },\n  methods: {\n    formatAmount: function formatAmount(s) {\n      if (s == undefined) {\n        s = 0;\n      }\n      var s1 = (s / 1000).toFixed(3);\n      return s1;\n    },\n    // 列表\n    getList: function getList(num) {\n      var _this = this;\n      this.loading = true;\n      this.searchFrom.page = num ? num : this.searchFrom.page;\n      (0, _chainTransferRecord.productShareRecordApi)(this.searchFrom).then(function (res) {\n        _this.tableData = res.list || [];\n        _this.searchFrom.total = res.total;\n        _this.loading = false;\n      }).catch(function () {\n        _this.loading = false;\n      });\n    },\n    getBrandList: function getBrandList() {\n      var _this2 = this;\n      var params = {\n        page: 1,\n        limit: 999999,\n        name: \"\",\n        type: \"-1\"\n      };\n      (0, _brand.brandLstApi)(params).then(function (res) {\n        _this2.brandList = res.list;\n      }).catch(function (res) {\n        _this2.$message.error(_this2.$t(\"common.fetchDataFailed\"));\n      });\n    },\n    //切换页数\n    pageChange: function pageChange(index) {\n      this.searchFrom.page = index;\n      this.getList();\n    },\n    //切换显示条数\n    sizeChange: function sizeChange(index) {\n      this.searchFrom.limit = index;\n      this.getList();\n    },\n    resetForm: function resetForm() {\n      this.searchFrom = {\n        keyword: \"\",\n        brandCode: \"\",\n        page: 1,\n        limit: 20,\n        total: 0\n      };\n      this.getList();\n    },\n    formatRate: function formatRate(s) {\n      return parseInt(s * 10000) / 100 + \"%\";\n    }\n  }\n};", null]}