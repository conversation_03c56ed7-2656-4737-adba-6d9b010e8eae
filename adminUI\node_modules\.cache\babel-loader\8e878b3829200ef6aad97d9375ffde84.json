{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\financial\\detail\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\financial\\detail\\index.vue", "mtime": 1754365042824}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _financial = require(\"@/api/financial\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: \"FinancialDetail\",\n  data: function data() {\n    return {\n      loading: false,\n      purchaseTableData: [],\n      tradeTableData: [],\n      timeList: [],\n      purchaseFrom: {\n        uid: \"\",\n        keyword: \"\",\n        rechargeType: \"\",\n        dateLimit: [],\n        payChannel: \"\",\n        walletCode: \"\",\n        bankName: \"\",\n        page: 1,\n        limit: 20,\n        total: 0\n      },\n      tradeFrom: {\n        keyword: \"\",\n        dateLimit: [],\n        linkId: \"\",\n        type: \"\",\n        page: 1,\n        limit: 20,\n        total: 0\n      },\n      tableFromType: \"purchase\",\n      rechargeTypeList: [{\n        label: \"agentFee\",\n        value: \"agent\"\n      }, {\n        label: \"partnerFee\",\n        value: \"partner\"\n      }],\n      walletList: [{\n        label: \"ShopeePay\",\n        value: \"ShopeePay\"\n      }, {\n        label: \"DANA\",\n        value: \"DANA\"\n      }, {\n        label: \"OVO\",\n        value: \"OVO\"\n      }, {\n        label: \"Gopay\",\n        value: \"Gopay\"\n      }],\n      bankList: []\n    };\n  },\n  created: function created() {},\n  mounted: function mounted() {\n    this.getList(this.tableFromType);\n    this.getBankList();\n  },\n  methods: {\n    // 获取银行列表\n    getBankList: function getBankList() {\n      var _this = this;\n      (0, _financial.extractBankApi)().then(function (res) {\n        _this.bankList = res;\n      }).catch(function () {});\n    },\n    getList: function getList(type, num) {\n      var _this2 = this;\n      this.loading = true;\n      if (type === \"purchase\") {\n        this.purchaseFrom.page = num ? num : this.purchaseFrom.page;\n        this.purchaseFrom.dateLimit = this.timeList.length ? this.timeList.join(\",\") : \"\";\n        (0, _financial.topUpLogListsApi)(this.purchaseFrom).then(function (res) {\n          _this2.purchaseTableData = res.list;\n          _this2.purchaseFrom.total = res.total;\n          _this2.loading = false;\n        });\n      } else {\n        this.tradeFrom.page = num ? num : this.tradeFrom.page;\n        this.tradeFrom.dateLimit = this.timeList.length ? this.timeList.join(\",\") : \"\";\n        (0, _financial.fundsMonitorListApi)(this.tradeFrom).then(function (res) {\n          _this2.tradeTableData = res.list;\n          _this2.tradeFrom.total = res.total;\n          _this2.loading = false;\n        });\n      }\n    },\n    //切换页数\n    pageChange: function pageChange(index, type) {\n      if (type === \"purchase\") {\n        this.purchaseFrom.page = index;\n      } else {\n        this.tradeFrom.page = index;\n      }\n      this.getList(type);\n    },\n    //切换显示条数\n    sizeChange: function sizeChange(index, type) {\n      if (type === \"purchase\") {\n        this.purchaseFrom.limit = index;\n      } else {\n        this.tradeFrom.limit = index;\n      }\n      this.getList(type);\n    },\n    handleReset: function handleReset() {\n      this.purchaseFrom = {\n        uid: \"\",\n        keyword: \"\",\n        rechargeType: \"\",\n        dateLimit: [],\n        payChannel: \"\",\n        walletCode: \"\",\n        bankName: \"\",\n        page: 1,\n        limit: 20,\n        total: 0\n      };\n      this.tradeFrom = {\n        keyword: \"\",\n        dateLimit: [],\n        linkId: \"\",\n        type: \"\",\n        page: 1,\n        limit: 20,\n        total: 0\n      };\n    },\n    handleUpload: function handleUpload() {}\n  }\n};", null]}